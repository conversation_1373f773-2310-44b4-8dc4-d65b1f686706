<template>
  <div class="flex items-start space-x-4">
    <div class="flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center" :class="iconBackgroundClass">
      <Icon :name="icon" class="w-6 h-6" :class="iconClass" />
    </div>
    <div class="flex-1">
      <h3 class="font-semibold mb-1" :class="titleClass">{{ title }}</h3>
      <div :class="contentClass">
        <slot>
          <p v-if="text">{{ text }}</p>
          <a v-else-if="link" :href="link.href" :class="linkClass">
            {{ link.text }}
          </a>
        </slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface LinkData {
  href: string
  text: string
}

interface Props {
  icon: string
  title: string
  text?: string
  link?: LinkData
  variant?: 'default' | 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default'
})

const iconBackgroundClass = computed(() => {
  const variants = {
    default: 'bg-primary/10',
    light: 'bg-secondary/20',
    dark: 'bg-primary/20'
  }
  return variants[props.variant]
})

const iconClass = computed(() => {
  const variants = {
    default: 'text-primary',
    light: 'text-secondary',
    dark: 'text-secondary'
  }
  return variants[props.variant]
})

const titleClass = computed(() => {
  const variants = {
    default: 'text-primary',
    light: 'text-secondary',
    dark: 'text-secondary'
  }
  return variants[props.variant]
})

const contentClass = computed(() => {
  const variants = {
    default: 'text-primary/80',
    light: 'text-secondary/80',
    dark: 'text-secondary/80'
  }
  return variants[props.variant]
})

const linkClass = computed(() => {
  const variants = {
    default: 'text-primary/80 hover:text-primary transition-colors',
    light: 'opacity-80 hover:opacity-100 transition-opacity',
    dark: 'opacity-80 hover:opacity-100 transition-opacity'
  }
  return variants[props.variant]
})
</script>
