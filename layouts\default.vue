<template>
  <div class="bg-background h-full w-full">
    <Navbar />
    <main class="pt-24 md:pt-0">
      <slot />
    </main>
  </div>
</template>

<script lang="ts" setup>
import Navbar from '~/components/ui/Navbar.vue';
</script>

<style>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Add scroll padding to account for fixed navbar */
section[id] {
  scroll-margin-top: 6rem; /* 96px for desktop navbar (h-24) */
}

/* For mobile, use smaller offset */
@media (max-width: 768px) {
  section[id] {
    scroll-margin-top: 5rem; /* 80px for mobile navbar (h-20) */
  }
}
</style>