<template>
  <SectionContainer
    :id="id"
    :title="title"
    :subtitle="subtitle"
    :variant="variant"
    :size="size"
  >

    <!-- Products Grid -->
    <div :class="gridClasses">
      <ProductCard
        v-for="product in products"
        :key="product.id || product.slug"
        :name="product.meta.title || 'Untitled'"
        :description="product.meta.description"
        :image-url="product.meta?.image"
        :price="product.meta?.price"
      />
    </div>

  </SectionContainer>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import ProductCard from '../ui/ProductCard.vue'
import SectionContainer from '../ui/SectionContainer.vue'

interface Props {
  id?: string
  title?: string
  subtitle?: string
  products: any
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  columns?: 1 | 2 | 3 | 4 | 5
  currency?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  columns: 3,
  currency: '$'
})

// Computed properties
const gridClasses = computed(() => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5'
  }

  return `grid ${columnClasses[props.columns]} gap-6 lg:gap-8`
})
</script>
