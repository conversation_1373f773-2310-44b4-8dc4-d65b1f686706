<template>
  <div v-if="isMounted" class="fixed bottom-6 right-6 z-40 flex flex-col space-y-3">
    <!-- WhatsApp Button -->
    <a
      v-if="whatsappNumber"
      :href="whatsappUrl"
      target="_blank"
      rel="noopener noreferrer"
      class="group relative w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
      :title="whatsappTooltip"
    >
      <Icon name="simple-icons:whatsapp" class="w-7 h-7" />
      
      <!-- Tooltip -->
      <div class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none shadow-lg">
        {{ whatsappTooltip }}
        <div class="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
      </div>
    </a>
    
    <!-- Phone Button -->
    <a
      v-if="phoneNumber"
      :href="phoneUrl"
      class="group relative w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
      :title="phoneTooltip"
    >
      <Icon name="heroicons:phone" class="w-7 h-7" />
      
      <!-- Tooltip -->
      <div class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none shadow-lg">
        {{ phoneTooltip }}
        <div class="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
      </div>
    </a>
    
    <!-- Email Button -->
    <a
      v-if="email"
      :href="emailUrl"
      class="group relative w-14 h-14 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
      :title="emailTooltip"
    >
      <Icon name="heroicons:envelope" class="w-7 h-7" />
      
      <!-- Tooltip -->
      <div class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none shadow-lg">
        {{ emailTooltip }}
        <div class="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
      </div>
    </a>
    
    <!-- Custom Actions -->
    <button
      v-for="(action, index) in customActions"
      :key="index"
      @click="handleCustomAction(action)"
      class="group relative w-14 h-14 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
      :class="action.bgColor || 'bg-primary hover:bg-primary/90'"
      :title="action.tooltip"
    >
      <Icon :name="action.icon" class="w-7 h-7" />
      
      <!-- Tooltip -->
      <div v-if="action.tooltip" class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none shadow-lg">
        {{ action.tooltip }}
        <div class="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
      </div>
    </button>
    
    <!-- Scroll to Top Button -->
    <button
      v-if="showScrollTop && isScrolled"
      @click="scrollToTop"
      class="group relative w-14 h-14 bg-gray-800 hover:bg-gray-900 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center transform hover:scale-110"
      title="Scroll to top"
    >
      <Icon name="heroicons:arrow-up" class="w-7 h-7" />

      <!-- Tooltip -->
      <div class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none shadow-lg">
        Scroll to top
        <div class="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
      </div>
    </button>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface CustomAction {
  icon: string
  tooltip?: string
  bgColor?: string
  action: () => void
}

interface Props {
  whatsappNumber?: string
  whatsappMessage?: string
  phoneNumber?: string
  email?: string
  customActions?: CustomAction[]
  showScrollTop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  whatsappMessage: 'Hello! I would like to know more about your services.',
  showScrollTop: true
})

const emit = defineEmits<{
  customAction: [action: CustomAction]
}>()

const isScrolled = ref(false)
const isMounted = ref(false)

const whatsappUrl = computed(() => {
  if (!props.whatsappNumber) return ''
  const cleanNumber = props.whatsappNumber.replace(/\D/g, '')
  const message = encodeURIComponent(props.whatsappMessage || '')
  return `https://wa.me/${cleanNumber}?text=${message}`
})

const phoneUrl = computed(() => {
  if (!props.phoneNumber) return ''
  return `tel:${props.phoneNumber}`
})

const emailUrl = computed(() => {
  if (!props.email) return ''
  return `mailto:${props.email}`
})

const whatsappTooltip = computed(() => {
  return `Chat on WhatsApp`
})

const phoneTooltip = computed(() => {
  return `Call ${props.phoneNumber}`
})

const emailTooltip = computed(() => {
  return `Send Email`
})

const handleScroll = () => {
  if (typeof window !== 'undefined') {
    isScrolled.value = window.scrollY > 300
  }
}

const scrollToTop = () => {
  if (typeof window !== 'undefined') {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
}

const handleCustomAction = (action: CustomAction) => {
  action.action()
  emit('customAction', action)
}

onMounted(() => {
  isMounted.value = true
  if (typeof window !== 'undefined') {
    window.addEventListener('scroll', handleScroll)
    handleScroll()
  }
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('scroll', handleScroll)
  }
})
</script>
