{"name": "one-pager-template", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/content": "^3.6.0", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^1.13.0", "@nuxt/image": "^1.10.0", "@nuxtjs/i18n": "^9.5.5", "@tailwindcss/vite": "^4.1.10", "firebase": "^11.9.1", "nuxt": "^3.17.5", "tailwindcss": "^4.1.10", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2"}}