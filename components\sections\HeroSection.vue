<template>
  <section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-background">
    <!-- Background -->
    <div class="absolute inset-0 z-0">
      <div v-if="backgroundImage" class="absolute inset-0">
        <NuxtImg
          :src="backgroundImage"
          :alt="backgroundAlt"
          class="w-full h-full object-cover"
          loading="eager"
        />
        <div class="absolute inset-0 bg-primary/10"></div>
      </div>
      <div v-else class="bg-gradient-to-br from-primary/5 via-background to-tertiary/30"></div>
    </div>
    
    <!-- Content -->
    <div class="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
      <div class="space-y-8">
        <!-- Main Heading -->
        <div class="space-y-4">
          <h1 class="text-4xl sm:text-5xl lg:text-7xl font-bold text-primary leading-tight">
            {{ title }}
          </h1>
          <p v-if="subtitle" class="text-xl sm:text-2xl lg:text-3xl text-primary/80 max-w-3xl mx-auto">
            {{ subtitle }}
          </p>
        </div>
        
        <!-- Description -->
        <p v-if="description" class="text-lg sm:text-xl text-primary/70 max-w-2xl mx-auto leading-relaxed">
          {{ description }}
        </p>
        
        <!-- Call to Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
          <Button
            v-if="primaryCta"
            :href="primaryCta.href"
            :to="primaryCta.to"
            size="xl"
            variant="primary"
            :icon="primaryCta.icon"
            @click="handlePrimaryCta"
          >
            {{ primaryCta.text }}
          </Button>
          
          <Button
            v-if="secondaryCta"
            :href="secondaryCta.href"
            :to="secondaryCta.to"
            size="xl"
            variant="outline"
            :icon="secondaryCta.icon"
            @click="handleSecondaryCta"
          >
            {{ secondaryCta.text }}
          </Button>
        </div>
        
        <!-- Features/Highlights -->
        <div v-if="features && features.length" class="pt-12">
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div
              v-for="(feature, index) in features"
              :key="index"
              class="flex flex-col items-center text-center space-y-3"
            >
              <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <Icon v-if="feature.icon" :name="feature.icon" class="w-8 h-8 text-primary" />
              </div>
              <h3 class="text-lg font-semibold text-primary">{{ feature.title }}</h3>
              <p class="text-primary/70 text-sm">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div
      class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20 transition-opacity duration-300"
      :class="{ 'opacity-0 pointer-events-none': isScrolled }"
    >
      <button
        class="flex items-center justify-center w-12 h-12 rounded-full bg-secondary/90 backdrop-blur-sm border-2 border-primary/30 shadow-lg"
        aria-label="Scroll to next section"
      >
        <Icon name="heroicons:chevron-down" class="w-6 h-6 text-primary" />
      </button>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Button from '../ui/Button.vue'

interface Feature {
  icon?: string
  title: string
  description: string
}

interface CtaButton {
  text: string
  href?: string
  to?: string
  icon?: string
}

interface Props {
  title: string
  subtitle?: string
  description?: string
  backgroundImage?: string
  backgroundAlt?: string
  primaryCta?: CtaButton
  secondaryCta?: CtaButton
  features?: Feature[]
}

defineProps<Props>()

const emit = defineEmits<{
  primaryCta: []
  secondaryCta: []
}>()

const isScrolled = ref(false)

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const handlePrimaryCta = () => {
  emit('primaryCta')
}

const handleSecondaryCta = () => {
  emit('secondaryCta')
}

const scrollToNext = () => {
  // Scroll to the next section (usually about section)
  if (typeof window !== 'undefined') {
    const heroHeight = window.innerHeight
    window.scrollTo({
      top: heroHeight,
      behavior: 'smooth'
    })
  }
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // Check initial scroll position
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
