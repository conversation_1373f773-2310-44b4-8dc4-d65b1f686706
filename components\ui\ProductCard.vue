<template>
  <div class="bg-secondary rounded-xl border border-primary/10 overflow-hidden">
    <!-- Image -->
    <div v-if="imageUrl" class="relative overflow-hidden">
      <NuxtImg
        :src="imageUrl"
        :alt="imageAlt"
        class="w-full h-48 object-scale-down"
        loading="lazy"
      />
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Name -->
      <h3 class="text-xl font-bold text-primary mb-2">{{ name }}</h3>

      <!-- Description -->
      <p v-if="description" class="text-primary/80 leading-relaxed mb-4">{{ description }}</p>

      <!-- Price -->
      <div v-if="price" class="text-2xl font-bold text-primary">
        {{ formattedPrice }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  name: string
  description?: string
  imageUrl?: string
  imageAlt?: string
  price?: number | string
  currency?: string
}

const props = withDefaults(defineProps<Props>(), {
  currency: '$',
  imageAlt: 'Product image'
})

// Computed properties
const formattedPrice = computed(() => {
  if (typeof props.price === 'number') {
    return `${props.currency}${props.price.toFixed(2)}`
  }
  return props.price
})
</script>
